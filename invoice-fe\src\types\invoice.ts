export interface Invoice {
  id: string;
  templateCode: string;
  invoiceSeri: string;
  invoiceNo: string;
  buyerName: string;
  creator: string;
  totalBeforeTax: number;
  taxAmount: number;
  totalAmount: number;
  secretCode: string;
  sentToTax: boolean;
  taxCode: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface GetInvoicesRequest {
  apiToken: string;
  startDate: string;
  endDate: string;
  rowPerPage: number;
  pageNum: number;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  buyerIdNo?: string;
  templateCode?: string;
  invoiceSeri?: string;
  getAll?: boolean;
  issueStartDate?: string;
  issueEndDate?: string;
  adjustmentType?: string;
}

export interface GetInvoicesResponse {
  totalRecords: number;
  pageNum: number;
  rowPerPage: number;
  invoices: Invoice[];
}

export interface InvoiceFilters {
  startDate?: string;
  endDate?: string;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  templateCode?: string;
  invoiceSeri?: string;
}

export interface InvoiceState {
  invoices: Invoice[];
  isLoading: boolean;
  error: string | null;
  filters: InvoiceFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface InvoiceStore extends InvoiceState {
  // Data fetching
  fetchInvoices: (params?: { page?: number; limit?: number }) => Promise<void>;

  // State management
  setFilters: (filters: Partial<InvoiceFilters>) => void;
  clearError: () => void;

  // Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}
