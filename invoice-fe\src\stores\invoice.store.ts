import { create } from "zustand";
import { toast } from "sonner";
import { InvoiceService } from "../services/invoice.service";
import { InvoiceStore, InvoiceFilters } from "../types/invoice";

export const useInvoiceStore = create<InvoiceStore>((set, get) => ({
  // Initial state
  invoices: [],
  isLoading: false,
  error: null,
  filters: {
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },

  // Data fetching
  fetchInvoices: async (params) => {
    set({ isLoading: true, error: null });
    try {
      const { filters, pagination } = get();
      const searchParams = {
        apiToken: localStorage.getItem("apiToken") || "",
        startDate: filters.startDate || new Date().toISOString().split("T")[0],
        endDate: filters.endDate || new Date().toISOString().split("T")[0],
        pageNum: params?.page || pagination.page,
        rowPerPage: params?.limit || pagination.limit,
        invoiceNo: filters.invoiceNo,
        invoiceType: filters.invoiceType,
        buyerTaxCode: filters.buyerTaxCode,
        templateCode: filters.templateCode,
        invoiceSeri: filters.invoiceSeri,
      };

      const response = await InvoiceService.getInvoices(searchParams);

      set({
        invoices: response.invoices || [],
        isLoading: false,
        pagination: {
          ...pagination,
          page: response.pageNum || 1,
          limit: response.rowPerPage || 10,
          total: response.totalRecords || 0,
          totalPages: Math.ceil(
            (response.totalRecords || 0) / (response.rowPerPage || 10)
          ),
        },
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || "Failed to fetch invoices",
      });
      toast.error(error.message || "Failed to fetch invoices");
    }
  },

  // State management
  setFilters: (filters: Partial<InvoiceFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
    // Automatically refetch with new filters
    get().fetchInvoices();
  },

  clearError: () => {
    set({ error: null });
  },

  // Pagination
  setPage: (page: number) => {
    // Validate page number
    const { pagination } = get();
    if (
      page < 1 ||
      (pagination.totalPages > 0 && page > pagination.totalPages)
    ) {
      return;
    }

    set((state) => ({
      pagination: { ...state.pagination, page },
    }));
    get().fetchInvoices({ page });
  },

  setLimit: (limit: number) => {
    // Validate limit
    if (limit < 1 || limit > 100) {
      return;
    }

    set((state) => ({
      pagination: { ...state.pagination, limit, page: 1 },
    }));
    get().fetchInvoices({ limit, page: 1 });
  },
}));
