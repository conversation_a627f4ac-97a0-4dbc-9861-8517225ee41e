import api from "../lib/api";
import { GetInvoicesRequest, GetInvoicesResponse } from "../types";

export class InvoiceService {
  static async getInvoices(
    params: GetInvoicesRequest
  ): Promise<GetInvoicesResponse> {
    // Validate pagination parameters
    if (params.pageNum < 0) {
      throw new Error("Page number must be 0 or greater");
    }

    if (params.rowPerPage < 1 || params.rowPerPage > 100) {
      throw new Error("Rows per page must be between 1 and 100");
    }

    // Validate required fields
    if (!params.apiToken) {
      throw new Error("API token is required");
    }

    if (!params.startDate || !params.endDate) {
      throw new Error("Start date and end date are required");
    }

    const response = await api.post<GetInvoicesResponse>(
      "/invoices/list",
      params
    );
    return response.data;
  }
}
