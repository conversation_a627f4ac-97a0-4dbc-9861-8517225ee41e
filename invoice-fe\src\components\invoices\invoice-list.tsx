import { useEffect, useState } from "react";
import { Search, RefreshCw } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { useInvoiceStore } from "../../stores/invoice.store";
import { Pagination } from "../ui/pagination";

export function InvoiceList() {
  const {
    invoices,
    isLoading,
    error,
    filters,
    pagination,
    fetchInvoices,
    setFilters,
    setPage,
    setLimit,
    clearError,
  } = useInvoiceStore();

  const [apiToken, setApiToken] = useState(
    localStorage.getItem("apiToken") || ""
  );

  useEffect(() => {
    if (apiToken) {
      localStorage.setItem("apiToken", apiToken);
      fetchInvoices();
    }
  }, [apiToken, fetchInvoices]);

  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  const handleSearch = () => {
    fetchInvoices();
  };

  const handleRefresh = () => {
    fetchInvoices();
  };

  const formatCurrency = (amount: number) => {
    // Handle undefined, null, or NaN values
    if (amount == null || isNaN(amount)) {
      return new Intl.NumberFormat("vi-VN", {
        style: "currency",
        currency: "VND",
      }).format(0);
    }

    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <div className="space-y-4">
      {/* API Token Input */}
      <Card>
        <CardHeader>
          <CardTitle>API Token</CardTitle>
          <CardDescription>
            Enter your API token to access invoice data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            value={apiToken}
            onChange={(e) => setApiToken(e.target.value)}
            type="password"
            placeholder="Enter your API token"
          />
        </CardContent>
      </Card>

      {/* Search Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Filters</CardTitle>
          <CardDescription>
            Filter invoices by date range and other criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <Input
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) => setFilters({ startDate: e.target.value })}
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">End Date</label>
                <Input
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) => setFilters({ endDate: e.target.value })}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Other Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Invoice Number</label>
                <Input
                  value={filters.invoiceNo || ""}
                  onChange={(e) => setFilters({ invoiceNo: e.target.value })}
                  placeholder="Enter invoice number"
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Template Code</label>
                <Input
                  value={filters.templateCode || ""}
                  onChange={(e) => setFilters({ templateCode: e.target.value })}
                  placeholder="Enter template code"
                  className="mt-1"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoice Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoices</CardTitle>
          <CardDescription>
            List of all invoices matching your search criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                <span className="text-sm text-muted-foreground">
                  Loading invoices...
                </span>
              </div>
            </div>
          ) : invoices.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No invoices found</p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>STT</TableHead>
                    <TableHead>Template Code</TableHead>
                    <TableHead>Invoice Series</TableHead>
                    <TableHead>Invoice No</TableHead>
                    <TableHead>Buyer Name</TableHead>
                    <TableHead>Creator</TableHead>
                    <TableHead className="text-right">
                      Total Before Tax
                    </TableHead>
                    <TableHead className="text-right">Tax Amount</TableHead>
                    <TableHead className="text-right">Total Amount</TableHead>
                    <TableHead>Secret Code</TableHead>
                    <TableHead>Tax Status</TableHead>
                    <TableHead>Tax Code</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice, index) => (
                    <TableRow key={invoice.id || `invoice-${index}`}>
                      <TableCell>
                        {((pagination.page || 1) - 1) *
                          (pagination.limit || 10) +
                          index +
                          1}
                      </TableCell>
                      <TableCell>{invoice.templateCode || "-"}</TableCell>
                      <TableCell>{invoice.invoiceSeri || "-"}</TableCell>
                      <TableCell>{invoice.invoiceNo || "-"}</TableCell>
                      <TableCell>{invoice.buyerName || "-"}</TableCell>
                      <TableCell>{invoice.creator || "-"}</TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(invoice.totalBeforeTax)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(invoice.taxAmount)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(invoice.totalAmount)}
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-2 py-1 rounded">
                          {invoice.secretCode || "-"}
                        </code>
                      </TableCell>
                      <TableCell>
                        {invoice.sentToTax ? "Sent" : "Pending"}
                      </TableCell>
                      <TableCell>{invoice.taxCode || "-"}</TableCell>
                      <TableCell>{invoice.status || "-"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <Pagination
                page={pagination.page}
                pageSize={pagination.limit}
                totalItems={pagination.total}
                totalPages={pagination.totalPages}
                onPageChange={setPage}
                onPageSizeChange={setLimit}
                pageSizeOptions={[10, 25, 50, 100]}
                className="mt-4"
                aria-label="Invoice pagination controls"
              />
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
