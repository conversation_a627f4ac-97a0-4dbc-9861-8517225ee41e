import {
  IsString,
  Is<PERSON><PERSON>ber,
  IsBoolean,
  IsO<PERSON>al,
  IsDateString,
  <PERSON><PERSON><PERSON>th,
  MaxLength,
  Min,
  Matches,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GetInvoicesDto {
  @ApiProperty({
    description: 'Customer API token for authentication',
    required: true,
  })
  @IsNotEmpty({ message: 'API token is required' })
  @IsString()
  apiToken: string;

  @ApiProperty({
    description: 'Invoice number',
    required: false,
    minLength: 7,
    maxLength: 35,
    pattern: '^[a-zA-Z0-9]*$',
  })
  @IsOptional()
  @IsString()
  @MinLength(7, { message: 'Invoice number must be at least 7 characters' })
  @MaxLength(35, { message: 'Invoice number must not exceed 35 characters' })
  @Matches(/^[a-zA-Z0-9]*$/, {
    message: 'Invoice number must contain only alphanumeric characters',
  })
  invoiceNo?: string;

  @ApiProperty({
    description: 'Start date for invoice search',
    required: true,
    maxLength: 50,
    example: '2024-01-01',
  })
  @IsNotEmpty({ message: 'Start date is required' })
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  @MaxLength(50, { message: 'Start date must not exceed 50 characters' })
  startDate: string;

  @ApiProperty({
    description: 'End date for invoice search',
    required: true,
    maxLength: 50,
    example: '2024-03-31',
  })
  @IsNotEmpty({ message: 'End date is required' })
  @IsDateString({}, { message: 'End date must be a valid date string' })
  @MaxLength(50, { message: 'End date must not exceed 50 characters' })
  endDate: string;

  @ApiProperty({
    description: 'Invoice type',
    required: false,
  })
  @IsOptional()
  @IsString()
  invoiceType?: string;

  @ApiProperty({
    description: 'Number of rows per page',
    required: true,
    minimum: 1,
    example: 10,
  })
  @IsNotEmpty({ message: 'Rows per page is required' })
  @IsNumber({}, { message: 'Rows per page must be a number' })
  @Min(1, { message: 'Rows per page must be at least 1' })
  rowPerPage: number;

  @ApiProperty({
    description: 'Page number (1-based)',
    required: true,
    minimum: 1,
    example: 1,
  })
  @IsNotEmpty({ message: 'Page number is required' })
  @IsNumber({}, { message: 'Page number must be a number' })
  @Min(0, { message: 'Page number must be at least 0' })
  pageNum: number;

  @ApiProperty({
    description: 'Buyer tax code',
    required: false,
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20, { message: 'Buyer tax code must not exceed 20 characters' })
  buyerTaxCode?: string;

  @ApiProperty({
    description: 'Buyer ID number',
    required: false,
  })
  @IsOptional()
  @IsString()
  buyerIdNo?: string;

  @ApiProperty({
    description: 'Template code',
    required: false,
  })
  @IsOptional()
  @IsString()
  templateCode?: string;

  @ApiProperty({
    description: 'Invoice series',
    required: false,
    maxLength: 25,
    pattern: '^[a-zA-Z0-9]*$',
  })
  @IsOptional()
  @IsString()
  @MaxLength(25, { message: 'Invoice series must not exceed 25 characters' })
  @Matches(/^[a-zA-Z0-9]*$/, {
    message: 'Invoice series must contain only alphanumeric characters',
  })
  invoiceSeri?: string;

  @ApiProperty({
    description: 'Get all invoices',
    required: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Get all must be a boolean value' })
  getAll?: boolean;

  @ApiProperty({
    description: 'Issue start date',
    required: false,
    maxLength: 50,
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Issue start date must be a valid date string' })
  @MaxLength(50, { message: 'Issue start date must not exceed 50 characters' })
  issueStartDate?: string;

  @ApiProperty({
    description: 'Issue end date',
    required: false,
    maxLength: 50,
    example: '2024-03-31',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Issue end date must be a valid date string' })
  @MaxLength(50, { message: 'Issue end date must not exceed 50 characters' })
  issueEndDate?: string;

  @ApiProperty({
    description: 'Adjustment type',
    required: false,
    maxLength: 1,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1, { message: 'Adjustment type must not exceed 1 character' })
  adjustmentType?: string;
}
